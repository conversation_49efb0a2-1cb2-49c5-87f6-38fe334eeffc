import { AuthGuardService } from './shared/services/auth-guard.service';
import { LayoutContainerComponent } from "./layouts/layout-container.component";
import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

const routes: Routes = [

  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'dashboard'
  },
  {
    path: "auth",
    loadChildren: () =>
      import("./auth/auth.module").then((m) => m.AuthModule),
  },
  {
    path: "dashboard",
    component: LayoutContainerComponent,
    loadChildren: () =>
      import("./dashboard/dashboard.module").then(
        (m) => m.dashboardmodule
      ),
    canActivate: [AuthGuardService], //TODO: decomment this line when be ready
    data: {
      authorities: ["ROLE_SUPERADMIN","ROLE_ASSISTANT"]
  },
  },
  {
    path: "references",
    // component: LayoutContainerComponent,
    loadChildren: () =>
      import("./references-app/references-app.module").then(
        (m) => m.ReferencesAppModule
      ),
  },

  {
    path: "**",
    redirectTo: "dashboard",
  },
    
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { useHash: false })],
  exports: [RouterModule],
})
export class AppRoutingModule { }
