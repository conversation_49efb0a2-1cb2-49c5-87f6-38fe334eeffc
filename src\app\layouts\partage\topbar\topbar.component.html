<!-- Topbar Start -->
<div class="navbar-custom px-0" [class]="cssClasses">
    <div class=" px-2 ">
        <!-- LOGO -->
        <a routerLink="/" class="topnav-logo" *ngIf="!hideLogo && topbarDark" tabindex="-1">
            <span class="topnav-logo-lg">
                <img src="assets/images/logo-light.png" alt="" height="16">
            </span>
            <span class="topnav-logo-sm">
                <img src="assets/images/logo_sm.png" alt="" height="16">
            </span>
        </a>

        <!-- LOGO -->
        <a routerLink="/" class="topnav-logo" *ngIf="!hideLogo && !topbarDark" tabindex="-1">
            <span class="topnav-logo-lg">
                <img src="assets/images/logo-dark.png" alt="" height="16">
            </span>
            <span class="topnav-logo-sm">
                <img src="assets/images/logo_sm_dark.png" alt="" height="16">
            </span>
        </a>
      

        <ul class="list-unstyled topbar-menu float-end mb-0">
   
            <li class="d-flex justify-content-center align-items-center mt-2">
                <div class="rounded-pill d-flex justify-content-center align-items-center "
                    style=" width:40px; height: 30px; "
                    [ngClass]="{ 'bg-danger':!network.isOnline}">
                    <!-- <span>{{network.isOnline ? '' : 'non '}}Connecté</span> -->
                    <i class="mdi" [ngClass]="{
                        ' mdi-access-point-network' : network.isOnline ,
                        ' mdi-signal-off' : !network.isOnline 
                    }"></i>

                    <!-- mdi-signal-off -->
                </div>
             
                <!-- <span class="p-1 bg-success rounded-pill">offline</span> -->
            </li>

            <li class="dropdown notification-list" ngbDropdown>

                <a ngbDropdownToggle class="nav-link nav-user arrow-none me-0" id="profileMenuDropdown" tabindex="-1"
                    href="javascript:void(0)" aria-expanded="false">
                    <span class="account-user-avatar">
                        <img src="assets/images/webfix-minimal.png" class="rounded-circle">

                    </span>
                    <span >
                        <!-- <span class="account-user-name">{{loggedInUser.name}}</span> -->
                        <span class="account-user-name">{{ loggedInTenant?.raisonSociale}}
                        </span>
                        <span class="account-position"> {{loggedInTenant?.prenom }} {{loggedInTenant?.nom}}

                        </span>
                        <!-- <span class="account-user-name text-capitalize">{{ loggedInUser?.username}}
                        </span> -->
                        <!-- {{loggedInUser?.mainAuthority | authorityName}} -->
                    </span>
                </a>
                <div ngbDropdownMenu aria-labelledby="profileMenuDropdown"
                    class="dropdown-menu-end dropdown-menu-animated topbar-dropdown-menu profile-dropdown me-2">
                    <!-- item -->
                    <div ngbDropdownItem class="dropdown-header noti-title">
                        <h6 class="text-overflow m-0">Bienvenue {{loggedInUser?.lastname}} </h6>
                    </div>
                    <!-- item -->
                    <a *ngFor="let option of profileOptions;" class="notify-item" ngbDropdownItem>
                        <i class="{{option.icon}} me-1"></i>
                        <span [routerLink]="[option.redirectTo]" style="cursor: pointer;">{{option.label}}</span>
                    </a>
                    <a *ngFor="let option of profileTenantOptions;" class="notify-item" ngbDropdownItem
                        [routerLink]="[option.redirectTo]">
                        <i class="{{option.icon}} me-1"></i>
                        <span style="cursor: pointer;">{{option.label}}</span>
                    </a>

                </div>
            </li>
        </ul>

        <!-- mobile menu toggle -->
        <button class="button-menu-mobile open-left d-none d-md-block" (click)="toggleSidebarWidth()" tabindex="-1"
            *ngIf="layoutType==='vertical'">
            <i class="mdi mdi-menu open-left-icon"></i>
        </button>
        <button class="button-menu-mobile open-left disable-btn" (click)="toggleMobileMenu($event)" tabindex="-1"
            *ngIf="layoutType==='vertical'">
            <i class="mdi mdi-menu open-left-icon"></i>
        </button>


        <a class="navbar-toggle open-left" [ngClass]="{'open': topnavCollapsed}" (click)="toggleMobileMenu($event)"
            tabindex="-1" *ngIf="layoutType==='horizontal'">
            <div class="lines">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </a>

        <a class="button-menu-mobile open-left disable-btn" (click)="toggleMobileMenu($event)" tabindex="-1"
            *ngIf="layoutType==='detached'">
            <div class="lines">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </a>

        <div class="app-search dropdown d-none d-lg-block" ngbDropdown container="body" placement="bottom-start">
           
        </div>

<div class="col-auto px-0 d-flex justify-content-center align-items-center mt-1 me-sm-2" >
    <ul class="list-unstyled mb-0 text-white d-inline-block px-1 px-sm-4 mt-md-0 mt-2 " style="background-color: #435F2C !important; border-radius: 10px;">
      <li class="notification-list d-flex align-items-center justify-content-center">
        <div *ngIf="loggedInTenant" class="text-center fw-semibold text-uppercase fs-6 d-block d-sm-none" >
            <p class="mb-0">{{ loggedInTenant?.username }}</p>
        </div>
        <div *ngIf="loggedInTenant" class="text-center fw-semibold text-uppercase d-none d-md-block  fs-3" >
            <p class="mb-0">{{ loggedInTenant?.username }}</p>
        </div>
      </li>
    </ul>
  </div>
  
    

    </div>

</div>
<!-- Topbar End -->