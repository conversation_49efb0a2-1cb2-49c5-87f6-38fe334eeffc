<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">Différence Prix Winplus</h4>
      <div class="d-flex flex-wrap justify-content-end gap-2">
        
      </div>
    </div>
  </div>
</div>


<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-body p-0">
        <kendo-grid class="content-wrap flex-shrink-0 ref-grid" [data]="diffPrixWinplusAndGroupe"
        style="height: calc(100vh - 130px);border-radius: 10px;" class="winClient-stats-grid clickable-grid ref-grid content-wrap custom-sort-grid"

          [pageable]="true"
          [pageSize]="navigation.pageSize"
          [skip]="navigation.skip"
          [sortable]="true"
          [sort]="sort"
          (sortChange)="sortChange($event)"
          >
          <kendo-grid-column field="codeGroupe" title="Code Groupe" [width]="100">
            <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="codeWinplus" title="Code Winplus" [width]="100">
             <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="designationGroupe" title="Designation Groupe" [width]="200">
             <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="designationWinplus" title="Designation Winplus" [width]="200">
             <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="prixVenteGroupe" title="Prix Vente Groupe" [width]="100" [sortable]="false"></kendo-grid-column>
          <kendo-grid-column field="prixVenteWinplus" title="Prix Vente Winplus" [width]="100"  [sortable]="false"></kendo-grid-column>
          <kendo-grid-column field="differencePrix" title="Difference Prix" [width]="100">
             <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="codeBarreGroupe" title="Code Barre Groupe" [width]="100"  [sortable]="false"></kendo-grid-column>
          <kendo-grid-column field="codeBarreWinplus" title="Code Barre Winplus" [width]="100"  [sortable]="false"></kendo-grid-column>
          <kendo-grid-column title="Action" [width]="100">
            <ng-template kendoGridCellTemplate let-dataItem>
              <app-action-icon [icon]="'check-circle'"
              appTooltip="Mettre à jour le prix Winplus"
              [backgroundColor]="'success'"
              [extendClass]="'circle-lg'" (click)="updatePrixWinplus(dataItem)"></app-action-icon>
            </ng-template>
          </kendo-grid-column>
          <ng-template
          kendoPagerTemplate
          let-totalPages="totalPages" let-currentPage="currentPage" let-total="total">
            <wph-grid-custom-pager
              [totalElements]="total"
              [totalPages]="totalPages"
              [currentPage]="currentPage"
              [allowPageSizes]="false"
              [navigation]="navigation"
              style="width: 100%;"
              (pageChange)="pageChange($event)"></wph-grid-custom-pager>
        ></ng-template>
      </kendo-grid>
      </div>
    </div>
  </div>
</div>
