import { Component, OnInit } from '@angular/core';
import { ProduitService } from '../../../Services/produit/produit.service';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
import { DataResult, SortDescriptor } from '@progress/kendo-data-query';
import { DiffPrixWinplusAndGroupe, UpdatePrixWinplus } from '../../../models/produit/ref-produits/diffPrixWinPlus';
import { UserInputService } from 'src/app/shared/services/user-input.service';
import { AlertService } from 'src/app/shared/services/alert.service';

@Component({
  selector: 'app-diff-prix-winplus',
  templateUrl: './diff-prix-winplus.component.html',
  styleUrls: ['./diff-prix-winplus.component.scss']
})
export class DiffPrixWinplusComponent implements OnInit {

  diffPrixWinplusAndGroupe: DataResult = {
    data: [],
    total: 0
  };
  navigation : Pagination = {
    pageSize :21,
    skip:0,
    sortField: '',
    sortMethod: '',
  };

  sort: SortDescriptor[] = [];


  constructor(
    private produitService: ProduitService,
    private alertService: AlertService,
    private userInputService: UserInputService,
  ) { }

  ngOnInit() {
    this.getDeffPrixWinplusAndGroupe();
  }

  getDeffPrixWinplusAndGroupe() {
    this.produitService.getDiffPrixWinplusAndGroupe(this.navigation, {}).subscribe({
      next: (res) => {
        this.diffPrixWinplusAndGroupe.data = res.content;
        this.diffPrixWinplusAndGroupe.total = res.totalElements;
      },
    })
  }

  pageChange(skip: number) {
    this.navigation.skip = skip;
    this.getDeffPrixWinplusAndGroupe();
  }


  updatePrixWinplus(dataItem: DiffPrixWinplusAndGroupe) {

    this.userInputService.confirm("Confirmer la mise à jour du prix Winplus ?", "Voulez-vous vraiment mettre à jour le prix Winplus ?","Oui","Non").then((res) => {
      if(res){
        const updatePrixWinplus = new UpdatePrixWinplus({
          codeWinplus: dataItem.codeWinplus,
          codeGroupe: dataItem.codeGroupe,
        });
        this.produitService.updatePrixWinplus(updatePrixWinplus).subscribe({
          next: (res) => {
            this.alertService.success("Prix Winplus mis à jour avec succès", "Prix Winplus");
            this.getDeffPrixWinplusAndGroupe();
          },
        });
      }
    }).catch(()=>{})
    
  }


   sortChange(sort: SortDescriptor[]) {
    this.sort = sort;
    if (this.sort.length > 0) {
      this.navigation.sortField = this.sort[0].field;
      this.navigation.sortMethod = this.sort[0].dir;
    } else {
      this.navigation.sortField = '';
      this.navigation.sortMethod = '';
    }
    this.getDeffPrixWinplusAndGroupe();
  }

}
