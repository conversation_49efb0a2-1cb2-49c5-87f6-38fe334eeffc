import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';

import { environment as env } from 'src/environments/environment';
import { Pagination, PaginationAndSorting } from 'src/app/references-app/referential/models/pagination.interface';
import { CommunService } from 'src/app/references-app/referential/services/commun.service';
import { FicheProduit } from '../../models/produit/base/ficheProduit.model';
import { Produit } from '../../models/produit/base/produit.model';
import { Page } from '../../models/Page/page.model';
import { ProduitCriteria } from '../../models/produit/base/produitCriteria.model';
import { SituationStock } from '../../models/statistiques/stock-inventaire/situationStock.model';
import { Stock } from '../../models/produit/stock/stock.model';
import { ProduitSiteCriteria, ProduitSiteCriteriaForm } from '../../models/produit/ref-produits/ProduitSiteCriteria.model';
import { ProduitWinplusCriteria } from '../../models/produit/ref-produits/produitsWinplus.model';
import { ProduitWinplusDto } from '../../models/produit/ref-produits/produitsWinplusDto.model';
import { EnvoiProduitRequest, TracabiliteEnvoiProduitResponse } from '../../models/produit/ref-produits/envoi-produit-request.model';
import { ProductStats, StatsCriteria } from '../../models/produit/stats/dashStats.model';
import { ProduitSite } from '../../models/produit/ref-produits/produitSite.model';
import { Categorie } from '../../models/produit/ref-produits/category.model';
import { AssociationsResponse, TAPCriteria } from '../../models/produit/ref-produits/tap.model';
import { DciCriteria,Dci } from '../../models/produit/ref-produits/dci.model';
import { Labo, LaboCriteria } from '../../models/produit/ref-produits/labo.model';
import { FormeProduit } from '../../models/produit/ref-produits/forme.model';
import { Noeud } from 'src/app/references-app/referential/models/tiers/fournisseur/noeud.model';
import { AlertProduit, CriteriaAlertProduit } from '../../models/produit/ref-produits/alertProduit.model';
import { tap } from 'rxjs/operators';
import { DiffPrixWinplusAndGroupe, UpdatePrixWinplus } from '../../models/produit/ref-produits/diffPrixWinPlus';

@Injectable({
  providedIn: 'root'
})
export class ProduitService {


  private _sites$ = new BehaviorSubject<Noeud[]>([]);
  sites$ = this._sites$.asObservable();




  constructor(private http: HttpClient, private communServ: CommunService) { }


  
    initializeSites() {
      this.getAllGrossiste().pipe(
        tap((response) => {
          this._sites$.next(response);
        })
      ).subscribe();
    }


  /* -------------------------- Save / Update Produit ------------------------- */
  saveProduit(prod: FicheProduit): Observable<FicheProduit> {
    return this.http.post<FicheProduit>(
      `${env.base_url}/api/winpharm/fiche-produit/edit`, prod, { observe: "body" }
    )
  }



  /* ------------------------  List Produits Paginated ------------------------ */
  listProduitsExtended(criteria: ProduitCriteria = {}, pagin: Pagination = {}, sortExpression: any = null): Observable<Page<Produit>> {

    let pageParams = {
      page: String(this.communServ.getPageNumber(pagin.skip, pagin.pageSize) ?? 0),
      size: String(pagin.pageSize) ?? 20,
    };

    if (sortExpression) {
      pageParams["sort"] = sortExpression;
    }
    return this.http.post<Page<Produit>>(
      `${env.base_url}/api/winpharm/produit/search`, criteria, {
      observe: "body",
      params: pageParams
    })
  }
  // get list not  paginated
  listProduits(criteria: ProduitCriteria = {}, pagin: Pagination = {}): Observable<Page<Produit>> {
    return this.http.post<Page<Produit>>(
      `${env.base_url}/api/winpharm/produit/search_list`, criteria, {
      observe: "body",
    })
  }

  /* --------------------- Get a product by id --------------------- */
  getProduitById(idProd: number): Observable<FicheProduit> {
    return this.http.get<FicheProduit>(
      `${env.base_url}/api/winpharm/fiche-produit/${idProd}`, {
      observe: "body"
    });
  }


  /* ------------------ Disabled Produit identified by its id ----------------- */
  disableProduit(idProd: number): Observable<FicheProduit> {
    return this.http.delete<FicheProduit>(
      `${env.base_url}/api/winpharm/produit/desactiver/${idProd}`
    )
  }

  /* ---------------------- activate produit using its id --------------------- */
  activateProduit(idProd: number): Observable<FicheProduit> {
    return this.http.get<FicheProduit>(
      `${env.base_url}/api/winpharm/produit/activer/${idProd}`
    )
  }



  /* ----------- getting the list of equivalent products ----------- */
  listProduitEquivalents(idProd: number): Observable<Produit[]> {
    return this.http.get<Produit[]>(
      `${env.base_url}/api/winpharm/produit/${idProd}/equivalents`, { observe: "body" }
    )
  }

  /* --------- Getting the list of autre forme d'un produit -------- */
  listProduitAutreFormes(idProd: number): Observable<Produit[]> {
    return this.http.get<Produit[]>(
      `${env.base_url}/api/winpharm/produit/${idProd}/toutesformes`, { observe: "body" }
    )
  }

  /* ---------------------- Load Inventaire of a product ---------------------- */
  loadProduitInventaire(idProd: number): Observable<SituationStock[]> {
    return this.http.get<SituationStock[]>(
      `${env.base_url}/api/winpharm/statistiques/stock/inventaire-produit/${idProd}`, { observe: "body" }
    )
  }


  /* ---------------------- Ajouter/Edit stock ---------------------- */
  editStockProduit(idProd, stock: Stock) {
    return this.http.post<Stock>(
      `${env.base_url}/api/winpharm/produit/${idProd}/stock/edit`, stock, {
      observe: "body",
    })
  }

  ///******************  suggérer produit $
  /// get by id
  //TODO:: HAFSA  CHANGE URL BASED ON API BACKEND 
  // since we already work with get produit 
  // 
  getSuggestionProduitById(idProd: number): Observable<FicheProduit> {

    return this.http.get<FicheProduit>(
      `${env.base_url}/api/winpharm/suggestion_produit/${idProd}`, {
      observe: "body"
    });
  }

  // save
  saveSuggestionProduit(prod): Observable<FicheProduit> {
    return this.http.post<FicheProduit>(
      `${env.base_url}/api/winpharm/suggestion_produit/edit`, prod, { observe: "body" }
    )
  }
  /* ------------------------  List Produits Suggested Paginated ------------------------ */

  rechercherSuggestionsProduits(criteria: ProduitCriteria = {}, pagin: Pagination = {}, sortExpression: any = null): Observable<Page<Produit>> {

    let pageParams = {
      page: String(this.communServ.getPageNumber(pagin.skip, pagin.pageSize) ?? 0),
      size: String(pagin.pageSize) ?? 20,
    };

    if (sortExpression) {
      pageParams["sort"] = sortExpression;
    }


    return this.http.post<Page<Produit>>(
      `${env.base_url}/api/winpharm/suggestion_produit/search`, criteria, {
      observe: "body",
      params: pageParams
    })
  }


  validerSuggestionProduit(idProd) {
    return this.http.get<FicheProduit>(
      `${env.base_url}/api/winpharm/suggestion_produit/valider/${idProd}`, {
      observe: "body"
    });
  }


  annulerSuggestionProduit(idProd, motif) {
    return this.http.delete<FicheProduit>(
      `${env.base_url}/api/winpharm/suggestion_produit/annuler/${idProd}`, {
      observe: "body",
      params: {
        id: idProd,
        motif: motif
      }
    });
  }

  associateProducts(data, single_result: boolean = false, origine: string = 'ocr', plus_associations: boolean = false, score = 10) {

    const params = new HttpParams()
      .set('single_result', single_result.toString())
      .set('origine', origine)
      .set('plus_associations', plus_associations.toString())
      .set('score', score)

    //console.log(`${env.tap_base_url}tap/associate-products`)

    return this.http.post<any>(`${env.tap_base_url}tap/associate-products`, data, { params });
  }

  //apiSend = "http://***************:8000/data/import-delta-products"
  sendProducts(data) {
    return this.http.post<any>(`${env.tap_base_url + "data/import-delta-products/"}`, data);
  }

  getPageNumber(skip: number, pageSize: number) {
    return (skip && pageSize) ? Math.floor(skip / pageSize) : 0;
  }
  /* ---------------------- win-ref: Get Produits Site by Criteria  ---------------------- */
  getProduitsSiteByCriteria(
    pagination: Pagination,
    criteria: Partial<ProduitSiteCriteriaForm> = {}
    
  ): Observable<Page<ProduitSite>> {
    let params = {
      page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
      size: String(pagination.pageSize ?? 20),
    }

    if (pagination.sortField) {
      params['sort'] = pagination.sortField == 'produitExtensions.codeSpecialEchange' ? 'produitExtensions.code' :pagination.sortField  + ',' + pagination.sortMethod;
    }

    return this.http.post<Page<ProduitSite>>(
      `${env.base_url}/api/win_ref_produit/interface_ref_pro/pro_site_search`,
      criteria,
      {
        observe: 'body',
        params: params
      }
    );
  }

  triggerMajProduit(): Observable<any> {
    return this.http.get<any>(`${env.base_url}/api/win_ref_produit/batch/maj-produits`, {
      observe: 'body'
    });
  }


  getAllCategories(): Observable<Categorie[]> {
    return this.http.get<Categorie[]>(`${env.base_url}/api/win_ref_produit/interface_ref_pro/getAllCategorie`);
  }

  getAllForms(): Observable<FormeProduit[]> {
    return this.http.get<FormeProduit[]>(`${env.base_url}/api/win_ref_produit/interface_ref_pro/getAllForme`);
  }

  getAllGrossiste(): Observable<Noeud[]> {
    return this.http.get<Noeud[]>(`${env.base_url}/api/win_ref_produit/interface_ref_pro/getAllGrossiste`);
  }


  searchDci(pagination:Pagination ,criteria:DciCriteria): Observable<Page<Dci>> {
    return this.http.post<Page<Dci>>(`${env.base_url}/api/win_ref_produit/interface_ref_pro/search_dci`, criteria, {
      observe: 'body',
      params:{
        page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
        size: String(pagination.pageSize ?? 20),
      }
    });
  }


  searchLabo(pagination:Pagination ,criteria:LaboCriteria): Observable<Page<Labo>> {
    return this.http.post<Page<Labo>>(`${env.base_url}/api/win_ref_produit/interface_ref_pro/search_labo`, criteria, {
      observe: 'body',
      params:{
        page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
        size: String(pagination.pageSize ?? 20),
      }
    });
  }



  searchProduitByAssistant(criteria:TAPCriteria[]): Observable<AssociationsResponse> {
    return this.http.post<AssociationsResponse>(`${env.base_url}/api/win_ref_produit/interface_ref_pro/associer`, criteria, {
      observe: 'body'
    });
  }

  getProduitsWinplusByCriteria(
    pagination: Pagination,
    criteria: ProduitWinplusCriteria = {}
  ): Observable<Page<ProduitWinplusDto>> {
    const params = new HttpParams()
    .set('page', String(Math.floor(pagination.skip / pagination.pageSize) ?? 0))
    .set('size', String(pagination.pageSize ?? 20))
    .set('sort', pagination.sortField ? `${pagination.sortField},${pagination.sortMethod || 'asc'}` : '');

    return this.http.post<Page<ProduitWinplusDto>>(
      `${env.base_url}/api/win_ref_produit/interface_ref_pro/pro_winplus_search`,
      criteria,
      {
        observe: 'body',
        params: params
      }
    );
  }

  envoieProduitWinplusToSite(request: EnvoiProduitRequest): Observable<void> {
    return this.http.post<void>(`${env.base_url}/api/win_ref_produit/interface_ref_pro/envoie_produits`, request, {
      observe: 'body'
    });
  }

  checkTracabiliteProduit(request: EnvoiProduitRequest): Observable<TracabiliteEnvoiProduitResponse[]> {
    return this.http.post<TracabiliteEnvoiProduitResponse[]>(`${env.base_url}/api/win_ref_produit/interface_ref_pro/check_tracabilite`, request, {
      observe: 'body'
    });
  }



  getProduitTranscoStats(statsCriteria:Partial<StatsCriteria> = {}): Observable<ProductStats> {
    return this.http.post<ProductStats>(`${env.base_url}/api/win_ref_produit/dash/stats`,statsCriteria, {
      observe: 'body'
    });
  }


  updateOrCreateProduitSite(produitSite: ProduitSite): Observable<ProduitSite> {
    return this.http.post<ProduitSite>(
      `${env.base_url}/api/win_ref_produit/interface_ref_pro/transco_pro_site`, produitSite, {
        observe: 'body'
      }
    );
  }

    updateOrCreateProduitGroupe(produitSite: ProduitSite): Observable<ProduitSite> {
    return this.http.post<ProduitSite>(
      `${env.base_url}/api/win_ref_produit/interface_ref_pro/createOrUpdate_pro_grp`, produitSite, {
        observe: 'body'
      }
    );
  }

  makeProductSiteForProcess(produitSite: ProduitSite,markForProcess): Observable<ProduitSite> {
  return this.http.post<ProduitSite>(
    `${env.base_url}/api/win_ref_produit/interface_ref_pro/declareOrRemoveProduitForTransco`, produitSite, {
      observe: 'body',
      params:{process:markForProcess ?? false}
    }
  );
}


getAlertProduit(pagination:Pagination,criteria:Partial<CriteriaAlertProduit> = {}): Observable<Page<AlertProduit>> {
  return this.http.post<Page<AlertProduit>>(`${env.base_url}/api/win_ref_produit/interface_ref_pro/getAlerteProduit`, criteria, {
    observe: 'body',
    params:{
      page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
      size: String(pagination.pageSize ?? 20),
    }
  });

}


// /api/win_ref_produit/interface_ref_pro/clotureAlerteOrNot

  clotureAlertProduit(alertProduit: AlertProduit): Observable<AlertProduit> {
    const action = alertProduit.dateClotureAlerte ? false : true;
    return this.http.post<AlertProduit>(
      `${env.base_url}/api/win_ref_produit/interface_ref_pro/clotureAlerteOrNot`, alertProduit, {
        observe: 'body',
        params: {
          action
        }
      }
    );
  }





  getDiffPrixWinplusAndGroupe(pagination:Pagination,criteria:Partial<DiffPrixWinplusAndGroupe> = {}): Observable<Page<DiffPrixWinplusAndGroupe>> {

    const sortField = pagination.sortField == 'differencePrix' ? 'prixDiff' : pagination.sortField;

    return this.http.post<Page<DiffPrixWinplusAndGroupe>>(`${env.base_url}/api/win_ref_produit/interface_ref_pro/get_diff_prix`,criteria, {
      observe: 'body',
      params:{
        page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
        size: String(pagination.pageSize ?? 20),
        sort: sortField ? `${sortField},${pagination.sortMethod}` : ''
      }
    });
  }

  updatePrixWinplus(updatePrixWinplus: UpdatePrixWinplus): Observable<ProduitWinplusDto> {
    return this.http.post<ProduitWinplusDto>(
      `${env.base_url}/api/win_ref_produit/interface_ref_pro/update_prix_winplus`, updatePrixWinplus, {
        observe: 'body'
      }
    );
  }


}

