interface DiffPrixWinplusAndGroupeDto {
    codeBarreGroupe: string;
    codeBarreWinplus: string; codeGroupe: string;
    codeWinplus: string; designationGroupe: string;
    designationWinplus: string; differencePrix: number;
    prixVenteGroupe: number;
    prixVenteWinplus: number;
}

export class DiffPrixWinplusAndGroupe implements DiffPrixWinplusAndGroupeDto {
    codeBarreGroupe: string;
    codeBarreWinplus: string; 
    codeGroupe: string;
    codeWinplus: string; 
    designationGroupe: string;
    designationWinplus: string;
    differencePrix: number;
    prixVenteGroupe: number; 
    prixVenteWinplus: number;
    constructor(diff: Partial<DiffPrixWinplusAndGroupeDto>) {
        Object.assign(this, diff);
    }
}



interface UpdatePrixWinplusDto {
    codeWinplus: string;
    codeGroupe: string;
}

export class UpdatePrixWinplus implements UpdatePrixWinplusDto {
    codeWinplus: string;
    codeGroupe: string;
    constructor(update: Partial<UpdatePrixWinplusDto>) {
        Object.assign(this, update);
    }
}










