import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { LayoutContainerComponent } from "src/app/layouts/layout-container.component";
import { AuthGuardService } from "src/app/shared/services/auth-guard.service";
import { DashboardComponent } from "./dashboard/dashboard/dashboard.component";

const routes: Routes = [
  {
    path: "produits",
    component: LayoutContainerComponent,
    loadChildren: () =>
      import("./produits/produits.module").then((m) => m.ProduitsModule),
    data: {
      authorities: ["ROLE_SUPERADMIN","ROLE_ASSISTANT"]
  },
    canActivate: [AuthGuardService],
  },


  {
    path: "dashboard",
    component: LayoutContainerComponent,
    loadChildren: () =>
      import("./dashboard/dashboard.module").then(
        (m) => m.DashboardModule
      ),

  },

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ReferencesProduitsRoutingModule { }
