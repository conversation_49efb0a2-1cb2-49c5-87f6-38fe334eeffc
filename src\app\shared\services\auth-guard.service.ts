import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Injectable } from '@angular/core';

import { AuthService } from './auth.service';
import { environment as env } from 'src/environments/environment';
@Injectable({
	providedIn: 'root'
})
export class AuthGuardService implements CanActivate {

	constructor(
		private router: Router,
		private authService: AuthService
	) { }



	canActivate(routeActive: ActivatedRouteSnapshot, _state: RouterStateSnapshot): boolean {
		// Get required authorities from route data
		const requiredAuthorities = routeActive.data.authorities as Array<string>;

		console.log('Required authorities for route:', requiredAuthorities);

		// Check if user is authenticated
		const isAuthenticated = this.authService.isAuthenticated();

		if (!isAuthenticated) {
			console.warn('User is not authenticated');
			this.router.navigateByUrl('/auth/login');
			return false;
		}

		// Get user's principal and authorities
		const principal = this.authService.getPrincipal();
		if (!principal) {
			console.warn('No principal found');
			this.router.navigateByUrl('/auth/login');
			return false;
		}

		const userAuthorities = principal.authorities;
		console.log('User authorities:', userAuthorities);

		// If route requires specific authorities, check them
		if (requiredAuthorities && requiredAuthorities.length > 0) {
			const hasRequiredAuthority = this.authService.hasAnyAuthority(requiredAuthorities);

			if (!hasRequiredAuthority) {
				if(userAuthorities.includes("ROLE_TECHNICO_COMMERCIAL")){
					this.router.navigateByUrl('/references/ref-clients/info-technique');
					return false;
				}else{
					console.warn(`User does not have required authorities: [${requiredAuthorities.join(', ')}]`);
					console.warn(`User has authorities: [${userAuthorities?.join(', ') || 'none'}]`);
					this.router.navigateByUrl('/page/error/page-not-found');
					return false;
				}
			}
		}

		// Check environment restriction if specified
		if (routeActive.data?.environmentName && env.environmentName !== routeActive.data.environmentName) {
			console.warn(`Environment mismatch. Required: ${routeActive.data.environmentName}, Current: ${env.environmentName}`);
			this.router.navigateByUrl('/page/error/page-not-found');
			return false;
		}

		console.log('Access granted');
		return true;
	}

	getResolvedUrl(route: ActivatedRouteSnapshot): string {
		return route.pathFromRoot
			.map(v => v.url.map(segment => segment.toString()).join('/'))
			.join('/');
	}
}
