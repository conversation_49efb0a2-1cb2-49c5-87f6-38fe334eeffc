import { Compo<PERSON>, ElementRef, HostL<PERSON>ener, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router, RouteConfigLoadStart, RouteConfigLoadEnd } from '@angular/router';
import { Title } from '@angular/platform-browser';
import firebase from 'firebase';
import { environment } from 'src/environments/environment';
import { AuthService } from './shared/services/auth.service';
import { EMPTY, Subscription } from 'rxjs';
import { Principal } from './shared/models/principal';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import Dexie from 'dexie';
import { NetworkStatusService } from './shared/services/network-status.service';
import { catchError } from 'rxjs/operators';

import { AlertService } from './shared/services/alert.service';
import { DataSyncIndexDbService } from './references-app/referential/services/data-sync-index-db.service';
import { ShortcutHandlerService } from './references-app/referential/services/shortcutHandler.service';
import { WinclientVilleService } from './references-app/references-clients/Services/winclient.ville.service';
import { WinclientSitesService } from './references-app/references-clients/Services/winclient.sites.service';
import { ProduitService } from './references-app/references-produits/Services/produit/produit.service';


@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit, OnDestroy {
  loadingRouteConfig: number = 0;

  private principal: Principal;
  private subscription: Subscription;

  disableApiRecording: boolean = environment.disableApiRecording;



  constructor(private router: Router, private titleService: Title,private produitService:ProduitService,
    private alertSrv: AlertService, private authService: AuthService, private syncService: DataSyncIndexDbService,
    private networkStats: NetworkStatusService,private winclientVillesService :WinclientVilleService, private winclientSitesService: WinclientSitesService,
    private shortcutService: ShortcutHandlerService, private elementRef: ElementRef) {



    // this.networkStats.onlineStatus$.subscribe(res => {
    //   if (res) {
    //     this.alertSrv.success("Vous êtes connecté", "MODAL")
    //   } else {
    //     this.alertSrv.error("Connexion interrompue,vous n'êtes pas connecté", "MODAL")
    //   }
    // })

    if (environment.enable_push_notification) {
      // updates.available.subscribe(_ => updates.activateUpdate().then(() => {
      //   console.log('reload for update');
      //   document.location.reload();
      // }));

      // push.messages.subscribe(msg => console.log('push message', msg));
      // push.notificationClicks.subscribe(click => console.log('notification click', click));
      // if (!firebase.apps.length) {
      //   console.log('firebase.initializeApp ****************');

      //   firebase.initializeApp(
      //       {
      //         apiKey: "AIzaSyBJF9YhQYOAc6rpYw9ogmpRAfKTHKved1I",
      //         authDomain: "testnotif-d5791.firebaseapp.com",
      //         projectId: "testnotif-d5791",
      //         storageBucket: "testnotif-d5791.appspot.com",
      //         messagingSenderId: "310510785785",
      //         appId: "1:310510785785:web:623cc4b2609f6cccb37e28",
      //         measurementId: "G-NT1FCPVWJY"
      //       });
      //   navigator.serviceWorker.getRegistration().then(swr => firebase.messaging().useServiceWorker(swr));


      //   const messaging = firebase.messaging();

      //   Notification.requestPermission()
      //       .then(() => messaging.getToken().then(token => console.log('msg token', token)))
      //       .catch(err => {
      //         console.log('Unable to get permission to notify.', err);
      //       });

      // }
    }
  }


  ngOnInit() {
    this.setFontScale();
    this.principal = this.authService.getPrincipal();
    this.refreshTitle();


    if(this.authService.hasAnyAuthority(["ROLE_ASSISTANT","ROLE_SUPERADMIN"])){
      this.winclientVillesService.initializeVilles();
      this.winclientSitesService.initializeSites();
      this.produitService.initializeSites();
    }

    this.subscription = this.authService.principal$.subscribe((principal) => {
      this.principal = principal;
      this.refreshTitle();
    });

    this.router.events.subscribe(event => {
      //this.modalServ.dismissAll();

      if (event instanceof RouteConfigLoadStart) {
        this.loadingRouteConfig = 1;
      } else if (event instanceof RouteConfigLoadEnd) {
        this.loadingRouteConfig = 0;
      }
    });


  }

  setFontScale() {
    const sacale = localStorage.getItem('scale')

    if (sacale && !isNaN(+sacale)) {
      document.documentElement.style.setProperty('--font-scale', sacale);
    }
  }




  refreshTitle() {
    let title = 'WinPlus Ref';

    if (this.principal) {
      title = this.principal.firstname + ' ' + this.principal.lastname + ' - ' + title;
    }

    this.titleService.setTitle(title);
  }


  ngOnDestroy() {
    if (this.subscription)
      this.subscription.unsubscribe();
    this.syncService.stopSync(); // Stop the sync when the component is destroyed;

    this.networkStats.destory()
  }

  @HostListener('window:keydown', ['$event'])
  handleKeyboardEvent($event: KeyboardEvent) {
    // Call the service method to handle the event
    this.shortcutService.handleKeyboardEvent($event, this.elementRef);
  }
}
